version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: nvh_mysql
    environment:
      MYSQL_ROOT_PASSWORD: nvh_password
      MYSQL_DATABASE: nvh_database
      MYSQL_USER: nvh_user
      MYSQL_PASSWORD: nvh_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped

  # Django后端
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: nvh_backend
    environment:
      - DEBUG=True
      - DB_HOST=mysql
      - DB_NAME=nvh_database
      - DB_USER=nvh_user
      - DB_PASSWORD=nvh_password
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
    depends_on:
      - mysql
    restart: unless-stopped
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8000"

  # Vue前端
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: nvh_frontend
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8000/api
    restart: unless-stopped

volumes:
  mysql_data:
