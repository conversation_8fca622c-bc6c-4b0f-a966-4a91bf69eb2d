@echo off
echo Starting NVH Data Management System Development Environment...

echo.
echo Starting MySQL database...
docker-compose up -d mysql

echo.
echo Waiting for MySQL to be ready...
timeout /t 10

echo.
echo Starting Django backend...
cd backend
if not exist .env (
    echo Creating .env file from template...
    copy .env.example .env
)

echo Installing Python dependencies...
pip install -r requirements.txt

echo Running database migrations...
python manage.py migrate

echo Creating superuser (if needed)...
python manage.py shell -c "
from django.contrib.auth import get_user_model;
User = get_user_model();
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created: admin/admin123')
else:
    print('Superuser already exists')
"

echo Starting Django development server...
start cmd /k "python manage.py runserver 8000"

cd ..

echo.
echo Starting Vue frontend...
cd frontend
if not exist .env (
    echo Creating .env file from template...
    copy .env.example .env
)

echo Installing Node.js dependencies...
npm install

echo Starting Vue development server...
start cmd /k "npm run dev"

cd ..

echo.
echo ========================================
echo Development environment started!
echo ========================================
echo Backend: http://localhost:8000
echo Frontend: http://localhost:5173
echo Admin: http://localhost:8000/admin
echo ========================================
echo.
echo Press any key to stop all services...
pause

echo Stopping services...
taskkill /f /im python.exe 2>nul
taskkill /f /im node.exe 2>nul
docker-compose down

echo Services stopped.
pause
