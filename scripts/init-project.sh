#!/bin/bash

echo "Initializing NVH Data Management System..."

echo ""
echo "Creating environment files..."

if [ ! -f backend/.env ]; then
    echo "Creating backend .env file..."
    cp backend/.env.example backend/.env
    echo "Backend .env file created. Please edit it with your database settings."
else
    echo "Backend .env file already exists."
fi

if [ ! -f frontend/.env ]; then
    echo "Creating frontend .env file..."
    cp frontend/.env.example frontend/.env
    echo "Frontend .env file created."
else
    echo "Frontend .env file already exists."
fi

echo ""
echo "Installing Python dependencies..."
cd backend
pip install -r requirements.txt
cd ..

echo ""
echo "Installing Node.js dependencies..."
cd frontend
npm install
cd ..

echo ""
echo "========================================"
echo "Project initialization completed!"
echo "========================================"
echo ""
echo "Next steps:"
echo "1. Edit backend/.env with your database settings"
echo "2. Start MySQL database: docker-compose up -d mysql"
echo "3. Run migrations: cd backend && python manage.py migrate"
echo "4. Create superuser: cd backend && python manage.py createsuperuser"
echo "5. Start development servers: ./scripts/start-dev.sh"
echo ""
echo "========================================"
