#!/usr/bin/env python3
"""
项目设置测试脚本
检查项目结构和依赖是否正确配置
"""

import os
import sys
import subprocess
import json

def check_file_exists(filepath, description):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} (文件不存在)")
        return False

def check_directory_exists(dirpath, description):
    """检查目录是否存在"""
    if os.path.isdir(dirpath):
        print(f"✅ {description}: {dirpath}")
        return True
    else:
        print(f"❌ {description}: {dirpath} (目录不存在)")
        return False

def check_python_package(package_name):
    """检查Python包是否已安装"""
    try:
        __import__(package_name)
        print(f"✅ Python包: {package_name}")
        return True
    except ImportError:
        print(f"❌ Python包: {package_name} (未安装)")
        return False

def check_node_modules():
    """检查Node.js依赖是否已安装"""
    package_json_path = "frontend/package.json"
    node_modules_path = "frontend/node_modules"
    
    if not os.path.exists(package_json_path):
        print(f"❌ package.json不存在: {package_json_path}")
        return False
    
    if not os.path.exists(node_modules_path):
        print(f"❌ node_modules不存在: {node_modules_path}")
        return False
    
    try:
        with open(package_json_path, 'r', encoding='utf-8') as f:
            package_data = json.load(f)
        
        dependencies = package_data.get('dependencies', {})
        key_packages = ['vue', 'vue-router', 'element-plus', 'axios', 'keycloak-js']
        
        all_installed = True
        for package in key_packages:
            if package in dependencies:
                print(f"✅ Node.js包: {package}")
            else:
                print(f"❌ Node.js包: {package} (未在package.json中找到)")
                all_installed = False
        
        return all_installed
    except Exception as e:
        print(f"❌ 检查Node.js依赖时出错: {e}")
        return False

def main():
    print("=" * 50)
    print("NVH数据管理系统 - 项目设置检查")
    print("=" * 50)
    
    all_checks_passed = True
    
    print("\n📁 检查项目结构...")
    structure_checks = [
        ("backend", "Django后端目录"),
        ("backend/nvh_backend", "Django项目目录"),
        ("backend/apps", "Django应用目录"),
        ("backend/apps/authentication", "认证应用目录"),
        ("backend/apps/core", "核心应用目录"),
        ("frontend", "Vue前端目录"),
        ("frontend/src", "Vue源码目录"),
        ("scripts", "脚本目录"),
    ]
    
    for path, desc in structure_checks:
        if not check_directory_exists(path, desc):
            all_checks_passed = False
    
    print("\n📄 检查关键文件...")
    file_checks = [
        ("backend/requirements.txt", "Python依赖文件"),
        ("backend/manage.py", "Django管理脚本"),
        ("backend/nvh_backend/settings.py", "Django设置文件"),
        ("frontend/package.json", "Node.js依赖文件"),
        ("frontend/vite.config.js", "Vite配置文件"),
        ("frontend/src/main.js", "Vue入口文件"),
        ("docker-compose.yml", "Docker编排文件"),
        ("README.md", "项目说明文件"),
    ]
    
    for filepath, desc in file_checks:
        if not check_file_exists(filepath, desc):
            all_checks_passed = False
    
    print("\n🐍 检查Python依赖...")
    python_packages = [
        'django',
        'djangorestframework',
        'django_cors_headers',
        'decouple',
        'pymysql',
        'requests',
        'jose',
    ]
    
    # 切换到backend目录检查Python包
    original_dir = os.getcwd()
    try:
        os.chdir('backend')
        for package in python_packages:
            if not check_python_package(package):
                all_checks_passed = False
    finally:
        os.chdir(original_dir)
    
    print("\n📦 检查Node.js依赖...")
    if not check_node_modules():
        all_checks_passed = False
    
    print("\n🔧 检查环境配置文件...")
    env_checks = [
        ("backend/.env.example", "后端环境配置模板"),
        ("frontend/.env.example", "前端环境配置模板"),
    ]
    
    for filepath, desc in env_checks:
        if not check_file_exists(filepath, desc):
            all_checks_passed = False
    
    # 检查是否已创建实际的.env文件
    if os.path.exists("backend/.env"):
        print("✅ 后端环境配置文件: backend/.env")
    else:
        print("⚠️  后端环境配置文件: backend/.env (建议从.env.example复制)")
    
    if os.path.exists("frontend/.env"):
        print("✅ 前端环境配置文件: frontend/.env")
    else:
        print("⚠️  前端环境配置文件: frontend/.env (建议从.env.example复制)")
    
    print("\n" + "=" * 50)
    if all_checks_passed:
        print("🎉 所有检查通过！项目设置正确。")
        print("\n下一步:")
        print("1. 配置环境变量文件 (.env)")
        print("2. 启动MySQL数据库")
        print("3. 运行数据库迁移")
        print("4. 启动开发服务器")
    else:
        print("❌ 部分检查未通过，请检查上述问题。")
        print("\n建议:")
        print("1. 运行 scripts/init-project.bat (Windows) 或 scripts/init-project.sh (Linux/Mac)")
        print("2. 安装缺失的依赖")
        print("3. 重新运行此检查脚本")
    print("=" * 50)
    
    return 0 if all_checks_passed else 1

if __name__ == "__main__":
    sys.exit(main())
