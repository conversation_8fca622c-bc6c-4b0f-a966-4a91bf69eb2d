#!/bin/bash

echo "Starting NVH Data Management System Development Environment..."

echo ""
echo "Starting MySQL database..."
docker-compose up -d mysql

echo ""
echo "Waiting for MySQL to be ready..."
sleep 10

echo ""
echo "Starting Django backend..."
cd backend

if [ ! -f .env ]; then
    echo "Creating .env file from template..."
    cp .env.example .env
fi

echo "Installing Python dependencies..."
pip install -r requirements.txt

echo "Running database migrations..."
python manage.py migrate

echo "Creating superuser (if needed)..."
python manage.py shell -c "
from django.contrib.auth import get_user_model;
User = get_user_model();
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created: admin/admin123')
else:
    print('Superuser already exists')
"

echo "Starting Django development server..."
python manage.py runserver 8000 &
DJANGO_PID=$!

cd ..

echo ""
echo "Starting Vue frontend..."
cd frontend

if [ ! -f .env ]; then
    echo "Creating .env file from template..."
    cp .env.example .env
fi

echo "Installing Node.js dependencies..."
npm install

echo "Starting Vue development server..."
npm run dev &
VUE_PID=$!

cd ..

echo ""
echo "========================================"
echo "Development environment started!"
echo "========================================"
echo "Backend: http://localhost:8000"
echo "Frontend: http://localhost:5173"
echo "Admin: http://localhost:8000/admin"
echo "========================================"
echo ""
echo "Press Ctrl+C to stop all services..."

# 等待用户中断
trap 'echo "Stopping services..."; kill $DJANGO_PID $VUE_PID; docker-compose down; exit' INT
wait
