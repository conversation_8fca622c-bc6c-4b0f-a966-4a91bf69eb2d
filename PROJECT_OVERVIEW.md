# NVH数据管理系统 - 项目概览

## 项目简介

NVH数据管理系统是一个基于Django + Vue.js的现代化Web应用，专门用于管理噪声、振动、声振粗糙度（NVH）相关数据。系统采用前后端分离架构，集成Keycloak进行统一身份认证。

## 技术架构

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue.js 前端   │    │  Django 后端    │    │   MySQL 数据库  │
│                 │    │                 │    │                 │
│ - Element Plus  │◄──►│ - REST API      │◄──►│ - 用户数据      │
│ - Keycloak Auth │    │ - Keycloak Auth │    │ - 业务数据      │
│ - Vite Build    │    │ - CORS Support  │    │ - 系统配置      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Keycloak 认证中心│
                    │                 │
                    │ - OAuth2/OIDC   │
                    │ - 用户管理      │
                    │ - 权限控制      │
                    └─────────────────┘
```

### 前端技术栈
- **Vue.js 3**: 采用Composition API，提供响应式用户界面
- **Element Plus**: 企业级UI组件库，提供丰富的组件
- **Vue Router 4**: 单页应用路由管理
- **Pinia**: 现代化状态管理库
- **Axios**: HTTP客户端，处理API请求
- **Vite**: 快速构建工具和开发服务器
- **Keycloak-js**: 前端认证适配器

### 后端技术栈
- **Django 4.2+**: Python Web框架
- **Django REST Framework**: RESTful API开发
- **MySQL 8.0+**: 关系型数据库
- **python-jose**: JWT token处理
- **django-cors-headers**: 跨域请求支持
- **PyMySQL**: MySQL数据库连接器

## 功能模块

### 1. 认证系统 (apps/authentication)
- **Keycloak集成**: OAuth2/OIDC认证流程
- **用户管理**: 扩展Django用户模型
- **Token验证**: JWT token验证和刷新
- **权限控制**: 基于角色的访问控制

### 2. 核心系统 (apps/core)
- **首页仪表板**: 系统概览和用户信息
- **系统监控**: 服务状态检查
- **基础模型**: 通用数据模型定义

### 3. 前端界面
- **响应式布局**: 适配不同屏幕尺寸
- **标签页导航**: 多页面标签管理
- **用户界面**: 现代化UI设计
- **国际化支持**: 中文界面

## 项目结构详解

```
nvh_django/
├── backend/                          # Django后端项目
│   ├── nvh_backend/                 # Django主项目配置
│   │   ├── __init__.py
│   │   ├── settings.py              # 项目设置
│   │   ├── urls.py                  # 主URL配置
│   │   ├── wsgi.py                  # WSGI配置
│   │   └── asgi.py                  # ASGI配置
│   ├── apps/                        # Django应用目录
│   │   ├── authentication/         # 认证应用
│   │   │   ├── models.py           # 用户模型
│   │   │   ├── views.py            # 认证视图
│   │   │   ├── authentication.py   # Keycloak认证类
│   │   │   ├── middleware.py       # 认证中间件
│   │   │   └── urls.py             # 认证URL配置
│   │   └── core/                   # 核心应用
│   │       ├── models.py           # 核心模型
│   │       ├── views.py            # 核心视图
│   │       └── urls.py             # 核心URL配置
│   ├── requirements.txt             # Python依赖
│   ├── manage.py                   # Django管理脚本
│   ├── .env.example               # 环境变量模板
│   └── Dockerfile                 # Docker构建文件
├── frontend/                        # Vue.js前端项目
│   ├── src/                        # 源代码目录
│   │   ├── views/                  # 页面组件
│   │   │   ├── Layout.vue         # 主布局组件
│   │   │   ├── Dashboard.vue      # 首页组件
│   │   │   ├── Business.vue       # 业务中心
│   │   │   ├── Permissions.vue    # 权限管理
│   │   │   └── Development.vue    # 待开发页面
│   │   ├── stores/                # Pinia状态管理
│   │   │   └── user.js           # 用户状态管理
│   │   ├── utils/                 # 工具函数
│   │   │   ├── keycloak.js       # Keycloak认证工具
│   │   │   └── request.js        # HTTP请求封装
│   │   ├── api/                   # API接口定义
│   │   │   ├── auth.js           # 认证API
│   │   │   └── core.js           # 核心API
│   │   ├── router/                # 路由配置
│   │   │   └── index.js          # 路由定义
│   │   ├── App.vue               # 根组件
│   │   └── main.js               # 应用入口
│   ├── public/                    # 静态资源
│   ├── package.json              # Node.js依赖
│   ├── vite.config.js           # Vite配置
│   ├── .env.example             # 前端环境变量模板
│   └── Dockerfile               # Docker构建文件
├── scripts/                       # 项目脚本
│   ├── init-project.bat         # Windows初始化脚本
│   ├── init-project.sh          # Linux/Mac初始化脚本
│   ├── start-dev.bat            # Windows开发启动脚本
│   ├── start-dev.sh             # Linux/Mac开发启动脚本
│   └── test-setup.py            # 项目设置检查脚本
├── docker-compose.yml            # Docker编排配置
├── .gitignore                   # Git忽略文件
├── README.md                    # 项目说明文档
└── PROJECT_OVERVIEW.md          # 项目概览文档（本文件）
```

## 开发流程

### 1. 环境准备
1. 安装Python 3.8+、Node.js 16+、MySQL 8.0+
2. 运行初始化脚本：`scripts/init-project.bat` 或 `scripts/init-project.sh`
3. 配置环境变量文件（.env）
4. 启动MySQL数据库

### 2. 开发启动
1. 运行开发脚本：`scripts/start-dev.bat` 或 `scripts/start-dev.sh`
2. 访问前端应用：http://localhost:5173
3. 访问后端API：http://localhost:8000/api
4. 访问管理后台：http://localhost:8000/admin

### 3. 开发规范
- **后端开发**: 遵循Django最佳实践，使用DRF构建API
- **前端开发**: 使用Vue 3 Composition API，遵循Element Plus设计规范
- **代码风格**: 保持代码整洁，添加必要注释
- **版本控制**: 使用Git进行版本管理

## 部署方案

### 开发环境
- 使用Docker Compose进行容器化部署
- 支持热重载和实时调试
- 集成开发工具和调试功能

### 生产环境
- 使用Nginx作为反向代理
- 配置HTTPS和SSL证书
- 使用Gunicorn运行Django应用
- 配置数据库连接池和缓存

## 安全考虑

### 认证安全
- 使用Keycloak进行统一身份认证
- JWT token自动刷新机制
- HTTPS加密传输

### 数据安全
- 数据库连接加密
- 敏感信息环境变量存储
- CORS跨域请求控制

### 权限控制
- 基于角色的访问控制（RBAC）
- API接口权限验证
- 前端路由权限守卫

## 扩展计划

### 短期目标
- 完善业务中心功能模块
- 实现权限管理系统
- 添加数据可视化图表
- 优化用户体验

### 长期目标
- 支持多租户架构
- 集成更多数据源
- 实现实时数据处理
- 移动端适配

## 技术支持

### 文档资源
- Django官方文档：https://docs.djangoproject.com/
- Vue.js官方文档：https://vuejs.org/
- Element Plus文档：https://element-plus.org/
- Keycloak文档：https://www.keycloak.org/documentation

### 社区支持
- GitHub Issues
- 技术论坛讨论
- 开发团队支持

---

*本文档持续更新，反映项目最新状态和技术架构。*
