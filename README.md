# NVH数据管理系统

一个基于Django + Vue.js的现代化Web应用，用于NVH（噪声、振动、声振粗糙度）数据管理。

## 技术栈

### 后端
- Django 4.2+
- Django REST Framework
- MySQL 8.0+
- Python 3.8+
- Keycloak OAuth2认证

### 前端
- Vue.js 3 (Composition API)
- Element Plus UI框架
- Vue Router 4
- Pinia状态管理
- Axios HTTP客户端
- Vite构建工具

## 项目结构

```
nvh_django/
├── backend/                    # Django后端
│   ├── nvh_backend/           # Django项目主目录
│   ├── apps/                  # Django应用目录
│   │   ├── authentication/   # 认证应用
│   │   └── core/             # 核心应用
│   ├── requirements.txt       # Python依赖
│   ├── manage.py             # Django管理脚本
│   └── .env                  # 环境变量
├── frontend/                  # Vue前端
│   ├── src/                  # Vue源码
│   │   ├── views/           # 页面组件
│   │   ├── stores/          # Pinia状态管理
│   │   ├── utils/           # 工具函数
│   │   └── api/             # API接口
│   ├── package.json          # Node.js依赖
│   ├── vite.config.js        # Vite配置
│   └── .env                  # 前端环境变量
├── docker-compose.yml        # Docker编排文件
├── README.md                 # 项目说明
└── scripts/                  # 启动脚本
    ├── start-dev.bat        # Windows启动脚本
    └── start-dev.sh         # Linux/Mac启动脚本
```

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- MySQL 8.0+
- Docker (可选)

### 1. 克隆项目
```bash
git clone <repository-url>
cd nvh_django
```

### 2. 配置环境变量

#### 后端配置
```bash
cd backend
cp .env.example .env
# 编辑 .env 文件，配置数据库和Keycloak信息
```

#### 前端配置
```bash
cd frontend
cp .env.example .env
# 编辑 .env 文件，配置API地址
```

### 3. 启动开发环境

#### 方式一：使用启动脚本（推荐）

**Windows:**
```bash
scripts/start-dev.bat
```

**Linux/Mac:**
```bash
chmod +x scripts/start-dev.sh
./scripts/start-dev.sh
```

#### 方式二：手动启动

**启动MySQL数据库:**
```bash
docker-compose up -d mysql
```

**启动Django后端:**
```bash
cd backend
pip install -r requirements.txt
python manage.py migrate
python manage.py createsuperuser
python manage.py runserver 8000
```

**启动Vue前端:**
```bash
cd frontend
npm install
npm run dev
```

### 4. 访问应用

- 前端应用: http://localhost:5173
- 后端API: http://localhost:8000/api
- Django管理后台: http://localhost:8000/admin

## 功能特性

### 已实现功能
- ✅ Keycloak OAuth2认证集成
- ✅ 用户认证和授权
- ✅ 响应式布局设计
- ✅ 标签页导航系统
- ✅ 用户信息管理
- ✅ 系统状态监控

### 待开发功能
- 🚧 业务中心模块
- 🚧 权限管理系统
- 🚧 数据可视化图表
- 🚧 文件上传下载
- 🚧 系统日志管理

## 认证配置

### Keycloak设置
项目使用Keycloak进行OAuth2认证，配置信息：

- 认证服务器: `https://account-test.sgmw.com.cn/auth/`
- Realm: `demo`
- 前端Client ID: `front` (public)
- 后端Client ID: `backend` (bearer-only)

### 测试账号
- 用户名: `test`
- 密码: `B5FDs0PcyuTipj^！`

## API文档

### 认证接口
- `GET /api/auth/config/` - 获取Keycloak配置
- `GET /api/auth/user/` - 获取当前用户信息
- `POST /api/auth/logout/` - 用户登出

### 核心接口
- `GET /api/core/dashboard/` - 获取首页数据
- `GET /api/core/status/` - 获取系统状态

## 开发指南

### 后端开发
1. 在 `backend/apps/` 目录下创建新的Django应用
2. 在 `settings.py` 中注册新应用
3. 配置URL路由
4. 实现视图和模型

### 前端开发
1. 在 `frontend/src/views/` 目录下创建新页面
2. 在 `router/index.js` 中配置路由
3. 在 `api/` 目录下添加API接口
4. 使用Element Plus组件构建UI

### 数据库迁移
```bash
cd backend
python manage.py makemigrations
python manage.py migrate
```

## 部署说明

### Docker部署
```bash
docker-compose up -d
```

### 生产环境配置
1. 设置 `DEBUG=False`
2. 配置生产数据库
3. 设置安全的SECRET_KEY
4. 配置静态文件服务
5. 设置HTTPS

## 故障排除

### 常见问题

1. **Keycloak认证失败**
   - 检查网络连接
   - 验证Keycloak配置信息
   - 确认客户端ID和密钥

2. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证数据库配置信息
   - 确认数据库权限

3. **前端无法访问后端API**
   - 检查CORS配置
   - 验证API地址配置
   - 确认后端服务运行状态

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。

## 联系方式

如有问题或建议，请联系开发团队。
