<template>
  <div class="permissions-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Lock /></el-icon>
          <span>权限管理</span>
        </div>
      </template>
      
      <div class="content-placeholder">
        <el-result
          icon="warning"
          title="权限管理"
          sub-title="此功能正在开发中，敬请期待..."
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/dashboard')">
              返回首页
            </el-button>
          </template>
        </el-result>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Lock } from '@element-plus/icons-vue'
</script>

<style scoped>
.permissions-management {
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.content-placeholder {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
