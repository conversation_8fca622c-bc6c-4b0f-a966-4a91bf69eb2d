<template>
  <div class="development">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-icon><Tools /></el-icon>
          <span>待开发功能</span>
        </div>
      </template>
      
      <div class="content-placeholder">
        <el-result
          icon="success"
          title="功能开发中"
          sub-title="更多精彩功能正在紧张开发中..."
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/dashboard')">
              返回首页
            </el-button>
          </template>
        </el-result>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { Tools } from '@element-plus/icons-vue'
</script>

<style scoped>
.development {
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.content-placeholder {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
