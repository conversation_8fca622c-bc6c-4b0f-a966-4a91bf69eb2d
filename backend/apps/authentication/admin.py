from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import User


@admin.register(User)
class CustomUserAdmin(UserAdmin):
    """
    自定义用户管理界面
    """
    list_display = ('username', 'email', 'preferred_username', 'keycloak_id', 'is_staff', 'is_active')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'email_verified')
    search_fields = ('username', 'email', 'preferred_username', 'keycloak_id')
    
    fieldsets = UserAdmin.fieldsets + (
        ('Keycloak信息', {
            'fields': ('keycloak_id', 'preferred_username', 'email_verified')
        }),
    )
