from django.db import models
from django.contrib.auth.models import AbstractUser


class User(AbstractUser):
    """
    扩展用户模型，用于存储Keycloak用户信息
    """
    keycloak_id = models.CharField(max_length=255, unique=True, null=True, blank=True)
    preferred_username = models.CharField(max_length=255, null=True, blank=True)
    email_verified = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'auth_user_extended'
