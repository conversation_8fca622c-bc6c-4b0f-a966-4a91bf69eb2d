"""
Keycloak认证类
"""
import requests
import json
from django.conf import settings
from django.contrib.auth import get_user_model
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from jose import jwt, JWTError


User = get_user_model()


class KeycloakAuthentication(BaseAuthentication):
    """
    Keycloak JWT Token认证
    """
    
    def authenticate(self, request):
        """
        认证用户
        """
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header or not auth_header.startswith('Bearer '):
            return None
            
        token = auth_header.split(' ')[1]
        
        try:
            # 验证token并获取用户信息
            user_info = self.verify_token(token)
            user = self.get_or_create_user(user_info)
            return (user, token)
        except Exception as e:
            raise AuthenticationFailed(f'Token验证失败: {str(e)}')
    
    def verify_token(self, token):
        """
        验证Keycloak token
        """
        try:
            # 获取Keycloak公钥
            keycloak_config = settings.KEYCLOAK_CONFIG
            realm_url = f"{keycloak_config['SERVER_URL']}realms/{keycloak_config['REALM']}"
            
            # 获取realm配置
            response = requests.get(f"{realm_url}/.well-known/openid-configuration")
            if response.status_code != 200:
                raise AuthenticationFailed('无法获取Keycloak配置')
            
            config = response.json()
            
            # 获取公钥
            jwks_response = requests.get(config['jwks_uri'])
            if jwks_response.status_code != 200:
                raise AuthenticationFailed('无法获取Keycloak公钥')
            
            jwks = jwks_response.json()
            
            # 解码token
            unverified_header = jwt.get_unverified_header(token)
            rsa_key = {}
            
            for key in jwks['keys']:
                if key['kid'] == unverified_header['kid']:
                    rsa_key = {
                        'kty': key['kty'],
                        'kid': key['kid'],
                        'use': key['use'],
                        'n': key['n'],
                        'e': key['e']
                    }
                    break
            
            if not rsa_key:
                raise AuthenticationFailed('无法找到匹配的公钥')
            
            # 验证token
            payload = jwt.decode(
                token,
                rsa_key,
                algorithms=['RS256'],
                audience=keycloak_config['CLIENT_ID'],
                issuer=config['issuer']
            )
            
            return payload
            
        except JWTError as e:
            raise AuthenticationFailed(f'Token解析失败: {str(e)}')
        except Exception as e:
            raise AuthenticationFailed(f'Token验证失败: {str(e)}')
    
    def get_or_create_user(self, user_info):
        """
        根据Keycloak用户信息获取或创建用户
        """
        keycloak_id = user_info.get('sub')
        username = user_info.get('preferred_username')
        email = user_info.get('email', '')
        
        try:
            # 尝试通过keycloak_id查找用户
            user = User.objects.get(keycloak_id=keycloak_id)
        except User.DoesNotExist:
            # 创建新用户
            user = User.objects.create(
                username=username,
                email=email,
                keycloak_id=keycloak_id,
                preferred_username=username,
                email_verified=user_info.get('email_verified', False),
                first_name=user_info.get('given_name', ''),
                last_name=user_info.get('family_name', ''),
            )
        
        return user
