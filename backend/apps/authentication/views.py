"""
认证相关视图
"""
from django.conf import settings
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status


@api_view(['GET'])
@permission_classes([AllowAny])
def keycloak_config(request):
    """
    获取Keycloak配置信息（前端使用）
    """
    config = {
        'url': settings.KEYCLOAK_CONFIG['SERVER_URL'],
        'realm': settings.KEYCLOAK_CONFIG['REALM'],
        'clientId': 'front',  # 前端使用public client
    }
    return Response(config)


@api_view(['GET'])
def user_info(request):
    """
    获取当前用户信息
    """
    if not request.user.is_authenticated:
        return Response({'error': '用户未认证'}, status=status.HTTP_401_UNAUTHORIZED)
    
    user_data = {
        'id': request.user.id,
        'username': request.user.username,
        'email': request.user.email,
        'first_name': request.user.first_name,
        'last_name': request.user.last_name,
        'preferred_username': getattr(request.user, 'preferred_username', ''),
        'is_staff': request.user.is_staff,
        'is_superuser': request.user.is_superuser,
    }
    
    return Response(user_data)


@api_view(['POST'])
def logout(request):
    """
    用户登出
    """
    # Keycloak登出需要在前端处理
    return Response({'message': '登出成功'})
