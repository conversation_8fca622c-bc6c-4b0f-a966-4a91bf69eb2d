"""
Keycloak认证中间件
"""
from django.http import JsonResponse
from django.conf import settings
from .authentication import KeycloakAuthentication


class KeycloakAuthenticationMiddleware:
    """
    Keycloak认证中间件
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.keycloak_auth = KeycloakAuthentication()
    
    def __call__(self, request):
        # 跳过不需要认证的路径
        skip_paths = [
            '/admin/',
            '/api/auth/config/',
            '/static/',
            '/media/',
        ]
        
        if any(request.path.startswith(path) for path in skip_paths):
            return self.get_response(request)
        
        # 对API路径进行认证检查
        if request.path.startswith('/api/'):
            try:
                auth_result = self.keycloak_auth.authenticate(request)
                if auth_result:
                    request.user, request.auth = auth_result
            except Exception as e:
                return JsonResponse({
                    'error': 'Authentication failed',
                    'message': str(e)
                }, status=401)
        
        response = self.get_response(request)
        return response
